.listItemStyle {
  display: flex;
  align-items: center;
  justify-content: space-between;
  :global {
    .aimi-list-item-extra {
      position: absolute;
      right: 24px;
      top: 24px;
    }
    .aimi-list-item-meta-title {
      margin-bottom: 0px !important;
    }
    .aimi-list-item-meta {
      margin-bottom: 8px !important;
    }
  }
}

.loadMoreStyle {
  cursor: pointer;
  color: var(--mc-color-text-secondary);
  gap: var(--mc-margin-xxs);
  // font-size: var(--mc-font-size-lg);
}

.secretBookCard {
  height: 100%;
  .secretBookCardHeader {
    .button {
      position: relative;
      background-color: transparent;
      border-radius: 32px;
      height: 32px;
      padding-left: calc(32px + 14px);
      padding-right: 23px;
      background-color: #010409;;
      border-color: #3d444d;
      .dot {
        position: absolute;
        top: -1px;
        left: -1px;
        width: 32px;
        height: 32px;
        border-radius: 50%;
        background-color: rgba(255, 255, 255, 0.1);
      }
      &:hover {
        background-color: rgba(101, 108, 118, 0.2);
      }
    }

    .activeButton {
      background-color: rgba(101, 108, 118, 0.5);
      border-color: #3d444d;
      .dot {
        background-color: rgba(255, 255, 255, 0.1);
      }
    }

    .newComerButton {
      border-color: rgba(255, 98, 0, 0.5);
      background: #0d1117;
      .dot {
        background-color: rgba(255, 95, 0, 0.25);
      }
      &.activeButton {
        background-color: #0D1117;
        border-color: #FF5F00;
        .dot {
          background-color: #FF5F00;
        }
      }
      &:hover {
        background: rgba(255, 98, 0, 0.2);
        border-color: rgba(255, 98, 0, 0.5);
        .dot {
          background-color: rgba(255, 95, 0, 0.25);
        }
      }
    }
  }
  .itemCard {
    :global {
      .aimi-list-header {
        background-color: rgba(13, 17, 23, 0.7);
        backdrop-filter: blur(5px);
        border-top-left-radius: calc(var(--mc-border-radius) * 2);
        border-top-right-radius: calc(var(--mc-border-radius) * 2);
      }
      .aimi-list-items {
        background-color: rgba(1, 4, 9, 0.7);
        backdrop-filter: blur(5px);
        border-bottom-left-radius: calc(var(--mc-border-radius) * 2);
        border-bottom-right-radius: calc(var(--mc-border-radius) * 2);
      }
    }
    .tryButton {
      margin-left: var(--mc-margin-xs);
    }
  }
}

